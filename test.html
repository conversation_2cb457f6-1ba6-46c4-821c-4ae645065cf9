<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AnySide 测试页面</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iIzM0OThmZiIvPgo8cGF0aCBkPSJNMTYgOEwyMiAxNkgxOFYyNEgxNFYxNkgxMEwxNiA4WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #3498ff;
            text-align: center;
        }
        .info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        .test-link {
            display: block;
            padding: 1rem;
            background: #3498ff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            text-align: center;
            transition: background 0.2s;
        }
        .test-link:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AnySide 测试页面</h1>
        
        <div class="info">
            <h3>📋 测试说明</h3>
            <p>这个页面用于测试 AnySide 扩展的标签页图标和标题显示功能。</p>
            <ul>
                <li>页面标题：AnySide 测试页面</li>
                <li>自定义图标：蓝色向上箭头</li>
                <li>测试时间：<span id="timestamp"></span></li>
            </ul>
        </div>

        <div class="test-links">
            <a href="https://www.google.com" class="test-link">Google</a>
            <a href="https://www.github.com" class="test-link">GitHub</a>
            <a href="https://www.stackoverflow.com" class="test-link">Stack Overflow</a>
            <a href="https://www.wikipedia.org" class="test-link">Wikipedia</a>
        </div>
    </div>

    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString('zh-CN');
        
        // 动态更新标题以测试标题变化检测
        let counter = 0;
        setInterval(() => {
            counter++;
            document.title = `AnySide 测试页面 (${counter})`;
        }, 5000);
    </script>
</body>
</html>
