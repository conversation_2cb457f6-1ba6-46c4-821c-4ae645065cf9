// AnySide 后台服务脚本

// Service Worker 生命周期事件
self.addEventListener('install', (event) => {
    console.log('Service Worker: 安装事件触发');
    // 强制 Service Worker 立即激活，跳过等待
    self.skipWaiting();
});

self.addEventListener('activate', (event) => {
    console.log('Service Worker: 激活事件触发');
    // 确保 Service Worker 在所有客户端中立即生效
    event.waitUntil(clients.claim());
});

// 监听 fetch 事件，即使不处理，也可以帮助 Service Worker 保持活跃
self.addEventListener('fetch', (event) => {
    // console.log('Service Worker: Fetch 事件触发', event.request.url);
    // 如果不需要特殊处理，可以不调用 event.respondWith
});

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(async () => {
    console.log('AnySide 扩展已安装');

    // 设置默认配置
    try {
        await chrome.storage.sync.set({
            defaultUrl: 'https://www.google.com',
            autoLoad: false,
            theme: 'dark'
        });
        console.log('默认配置已设置');
    } catch (error) {
        console.error('设置默认配置失败:', error);
    }
});

// 处理扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
    console.log('扩展图标被点击。尝试打开侧边栏...');
    try {
        // 打开侧边栏
        await chrome.sidePanel.open({ windowId: tab.windowId });
        console.log('侧边栏已成功打开 (通过图标点击)。');
    } catch (error) {
        console.error('通过图标点击打开侧边栏失败:', error);
    }
});

// 监听来自内容脚本和侧边栏的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('收到消息:', request);
    
    switch (request.action) {
            
        case 'getCurrentTab':
            handleGetCurrentTab(request, sender, sendResponse);
            break;
            
        case 'loadUrlInSidePanel':
            handleLoadUrlInSidePanel(request, sender, sendResponse);
            break;
            
        case 'getPageContent':
            handleGetPageContent(request, sender, sendResponse);
            break;
            
        default:
            console.log('未知消息类型:', request.action);
    }
    
    return true; // 保持消息通道开放
});


// 处理获取当前标签页信息请求
async function handleGetCurrentTab(_request, _sender, sendResponse) {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        sendResponse({
            success: true,
            tab: {
                id: tab.id,
                url: tab.url,
                title: tab.title,
                favIconUrl: tab.favIconUrl
            }
        });
    } catch (error) {
        console.error('获取当前标签页失败:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// 处理在侧边栏中加载URL请求
async function handleLoadUrlInSidePanel(request, _sender, sendResponse) {
    try {
        // 直接向侧边栏发送消息，侧边栏会处理在新标签页中加载URL的逻辑
        // 侧边栏的 handleMessage 会接收 'loadUrlInSidePanel' 消息
        const sidePanelTabs = await chrome.tabs.query({ url: chrome.runtime.getURL("sidepanel.html") });
        if (sidePanelTabs.length > 0) {
            await chrome.tabs.sendMessage(sidePanelTabs[0].id, {
                action: 'loadUrlInSidePanel',
                url: request.url
            });
            sendResponse({ success: true });
        } else {
            // 如果侧边栏未打开，则先存储URL，待侧边栏打开后加载
            await chrome.storage.local.set({
                pendingUrl: request.url,
                loadTimestamp: Date.now()
            });
            sendResponse({ success: true });
        }
    } catch (error) {
        console.error('加载URL失败:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// 处理获取页面内容请求
async function handleGetPageContent(_request, _sender, sendResponse) {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        // 向内容脚本发送消息获取页面内容
        const response = await chrome.tabs.sendMessage(tab.id, {
            action: 'getPageContent'
        });

        sendResponse({ success: true, content: response });
    } catch (error) {
        console.error('获取页面内容失败:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// 监听标签页更新事件
chrome.tabs.onUpdated.addListener((_tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        // 通知侧边栏标签页已更新
        chrome.runtime.sendMessage({
            action: 'tabUpdated',
            tab: {
                id: tab.id,
                url: tab.url,
                title: tab.title
            }
        }).catch(() => {
            // 忽略错误，可能侧边栏未打开
        });
    }
});

// 监听标签页激活事件
chrome.tabs.onActivated.addListener(async (activeInfo) => {
    try {
        const tab = await chrome.tabs.get(activeInfo.tabId);
        
        // 通知侧边栏活动标签页已改变
        chrome.runtime.sendMessage({
            action: 'activeTabChanged',
            tab: {
                id: tab.id,
                url: tab.url,
                title: tab.title
            }
        }).catch(() => {
            // 忽略错误，可能侧边栏未打开
        });
    } catch (error) {
        console.error('处理标签页激活事件失败:', error);
    }
});
